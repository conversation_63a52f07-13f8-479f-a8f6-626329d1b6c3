import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useGameStore, Card as PlayingCard } from "@/store/gameStore";
import { Card } from "@/components/ui/card";
import { getCardImagePath } from "@/utils/cardUtils";
import socketService from "@/services/socketService";
import gameService from '../services/gameService';

export default function CardPassingModal() {
  const { players, hand, isDealer, playCard } = useGameStore();

  const [gamePhase, setGamePhase] = useState<string>("idle");
  const [selectedCardsToPass, setSelectedCardsToPass] = useState<PlayingCard[]>(
    []
  );
  const [cardsDealt, setCardsDealt] = useState(0);
  const [showModal, setShowModal] = useState(false);

  // Simulate phases based on cards dealt
  useEffect(() => {
    if (cardsDealt === 0) {
      setGamePhase("idle");
    } else if (cardsDealt < 16) {
      // 4 cards × 4 players
      setGamePhase("passingInitialFour");
    } else if (cardsDealt < 24) {
      // 6 cards × 4 players
      setGamePhase("passingFinalTwo");
    } else {
      setGamePhase("dealingComplete");

      // If we want to show the modal after dealing is complete
      // setShowModal(true);
    }
  }, [cardsDealt]);

  // State to track the current player receiving a card
  const [currentPlayerInfo, setCurrentPlayerInfo] = useState<{
    name: string;
    team: number;
    card?: { suit: string; value: string };
  } | null>(null);

  // Listen for dealing_card_to events from the server
  useEffect(() => {
    const handleDealingCardTo = (data: {
      playerId: string;
      playerName: string;
      playerTeam: number;
    }) => {
      // Update the current player info
      setCurrentPlayerInfo({
        name: data.playerName,
        team: data.playerTeam
      });
    };

    // Add socket event listener
    gameService.on("dealing_card_to", handleDealingCardTo);

    // Clean up
    return () => {
      gameService.off("dealing_card_to", handleDealingCardTo);
    };
  }, []);

  // Listen for card_dealt events from the server
  useEffect(() => {
    const handleCardDealt = (data: {
      playerId: string;
      playerName: string;
      playerTeam: number;
      card: { suit: string; value: string };
      cardsDealtToPlayer: number;
      totalCardsDealt: number;
    }) => {
      // Increment the cards dealt counter
      setCardsDealt(data.totalCardsDealt);

      // Update the current player info with the card
      setCurrentPlayerInfo({
        name: data.playerName,
        team: data.playerTeam,
        card: data.card
      });

      console.log("Card dealt event received in CardPassingModal:", data);
    };

    const handleDealingComplete = () => {
      console.log("Dealing complete event received in CardPassingModal");
      setGamePhase("dealingComplete");

      // Show the modal after a delay if needed
      // setTimeout(() => {
      //   setShowModal(true);
      // }, 1000);
    };

    // Add socket event listener
    gameService.on("card_dealt", handleCardDealt);
    window.addEventListener("cards_dealing_complete", handleDealingComplete);

    // Clean up
    return () => {
      gameService.off("card_dealt", handleCardDealt);
      window.removeEventListener(
        "cards_dealing_complete",
        handleDealingComplete
      );
    };
  }, []);

  // Determine if the modal should be shown
  const isCardPassingPhase = gamePhase.includes("Passing");

  // Debug logging
  useEffect(() => {
    console.log("CardPassingModal - Current game phase:", gamePhase);
    console.log(
      "CardPassingModal - Is card passing phase:",
      isCardPassingPhase
    );
    console.log("CardPassingModal - Current dealer:", isDealer ? "Yes" : "No");
  }, [gamePhase, isCardPassingPhase, isDealer]);

  // Get the appropriate title and instructions based on the game phase
  const getPhaseInfo = () => {
    // Base information
    let info = {
      title: "Card Passing",
      instructions: "Cards are being passed automatically in anti-clockwise order",
      cardsNeeded: 0,
      isDealer: isDealer,
    };

    // Add current player info if available
    if (currentPlayerInfo) {
      if (currentPlayerInfo.card) {
        // If we have card info, show what card was dealt to which player
        info.instructions = `${currentPlayerInfo.name} (Team ${currentPlayerInfo.team}) received ${currentPlayerInfo.card.value} of ${currentPlayerInfo.card.suit}. ${cardsDealt}/24 cards dealt.`;
      } else {
        // If we don't have card info yet, show which player is about to receive a card
        info.instructions = `Dealing to ${currentPlayerInfo.name} (Team ${currentPlayerInfo.team}). ${cardsDealt}/24 cards dealt.`;
      }
    }

    // Add phase-specific information
    if (gamePhase === "passingInitialFour") {
      info.title = "Dealing First 4 Cards";
      info.instructions = `${info.instructions} - Dealing first 4 cards to each player (${cardsDealt}/16)`;
    } else if (gamePhase === "passingFinalTwo") {
      info.title = "Dealing Final 2 Cards";
      info.instructions = `${info.instructions} - Dealing final 2 cards to each player (${cardsDealt}/24)`;
    } else if (gamePhase === "dealingComplete") {
      info.title = "Dealing Complete";
      info.instructions = "All cards have been dealt. Game is ready to begin.";
    }

    return info;
  };

  const { title, instructions, cardsNeeded } = getPhaseInfo();

  // Don't show the modal since card passing is now automatic
  const shouldShowModal = showModal;

  // Debug logging for visibility
  useEffect(() => {
    console.log("CardPassingModal - Should show modal:", shouldShowModal);
    console.log("CardPassingModal - Phase info:", {
      title,
      instructions,
      cardsNeeded,
      isDealer,
    });
  }, [shouldShowModal, title, instructions, cardsNeeded, isDealer]);

  // Handle card selection
  const handleCardClick = (card: PlayingCard) => {
    if (selectedCardsToPass.some((c) => c.id === card.id)) {
      // Remove card if already selected
      setSelectedCardsToPass(
        selectedCardsToPass.filter((c) => c.id !== card.id)
      );
    } else if (selectedCardsToPass.length < cardsNeeded) {
      // Add card if not already selected and we haven't reached the limit
      setSelectedCardsToPass([...selectedCardsToPass, card]);
    }
  };

  // Handle confirm button
  const handleConfirm = () => {
    if (selectedCardsToPass.length === cardsNeeded) {
      // Pass the selected cards
      selectedCardsToPass.forEach((card) => {
        playCard(card);
      });

      // Close the modal
      setShowModal(false);
    }
  };

  if (!shouldShowModal) {
    return null;
  }

  return (
    <AnimatePresence>
      {shouldShowModal && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/70"
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-gray-900 rounded-xl shadow-2xl border-2 border-[#E1C760] w-[90%] max-w-2xl max-h-[90vh] overflow-hidden"
            style={{ boxShadow: "0 0 30px rgba(225, 199, 96, 0.3)" }}
          >
            {/* Header */}
            <div className="bg-[#E1C760] px-4 py-3 flex justify-between items-center">
              <h2 className="text-black font-bold text-xl">{title}</h2>
              <div className="text-black text-sm font-medium">
                {selectedCardsToPass.length}/{cardsNeeded} Cards Selected
              </div>
            </div>

            {/* Content */}
            <div className="p-4 overflow-y-auto max-h-[calc(90vh-60px)]">
              <div className="bg-gray-800 p-3 mb-4 rounded">
                <p className="text-gray-300 mb-4">{instructions}</p>

                {/* Card Selection Area */}
                <div className="flex flex-wrap justify-center gap-2 mt-4">
                  {hand.map((card) => (
                    <motion.button
                      key={card.id}
                      whileHover={{ y: -10 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => handleCardClick(card)}
                      className={`relative w-[70px] transition-all duration-200 ${
                        selectedCardsToPass.some((c) => c.id === card.id)
                          ? "ring-2 ring-[#E1C760] transform -translate-y-4"
                          : ""
                      }`}
                    >
                      <Card className="aspect-[2/3] w-full flex items-center justify-center bg-white hover:shadow-lg">
                        <img
                          src={getCardImagePath(card.value, card.suit)}
                          alt={`${card.value} of ${card.suit}`}
                          className="w-full h-full object-contain"
                        />
                      </Card>
                      {selectedCardsToPass.some((c) => c.id === card.id) && (
                        <div className="absolute -top-2 -right-2 bg-[#E1C760] text-black text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center">
                          {selectedCardsToPass.findIndex(
                            (c) => c.id === card.id
                          ) + 1}
                        </div>
                      )}
                    </motion.button>
                  ))}
                </div>

                {/* Confirm Button */}
                <div className="mt-6 flex justify-center">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={handleConfirm}
                    disabled={selectedCardsToPass.length !== cardsNeeded}
                    className={`px-6 py-2 rounded-lg font-bold ${
                      selectedCardsToPass.length === cardsNeeded
                        ? "bg-[#E1C760] text-black hover:bg-[#E1C760]/90"
                        : "bg-gray-700 text-gray-400 cursor-not-allowed"
                    }`}
                  >
                    Confirm Selection
                  </motion.button>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
