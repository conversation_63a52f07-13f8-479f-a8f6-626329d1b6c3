"use client";
import { useState, useEffect } from "react";
import { useSpectatorStore } from "@/store/spectatorStore";
import { useGameStore } from "@/store/gameStore";
import { Card } from "@/components/ui/card";
import { getCardImagePath } from "@/utils/cardUtils";
import { Button } from "@/components/ui/button";
import { Eye, EyeOff, X } from "lucide-react";
import { useNavigate } from "react-router-dom";
// import socketService from "@/services/socketService";
import gameService from '../services/gameService';

export default function SpectatorView() {
  const { isSpectator, gameCode, spectatorName, gameLobby, playerCards, leaveSpectatorMode } = useSpectatorStore();
  const { players, trumpSuit } = useGameStore();
  const [showAllCards, setShowAllCards] = useState(true);
  const navigate = useNavigate();

  // Track played cards
  const [playedCards, setPlayedCards] = useState<any[]>([]);
  const [currentTurnPlayerId, setCurrentTurnPlayerId] = useState<string | null>(null);

  // Add debugging and set up event listeners
  useEffect(() => {
    console.log('SpectatorView - isSpectator:', isSpectator);
    console.log('SpectatorView - gameCode:', gameCode);
    console.log('SpectatorView - spectatorName:', spectatorName);
    console.log('SpectatorView - gameLobby:', gameLobby);
    console.log('SpectatorView - playerCards:', playerCards);
    console.log('SpectatorView - players:', players);

    // Set up socket listeners for spectator updates
    gameService.on('spectator_game_state', (data) => {
      console.log('SpectatorView - Received spectator_game_state:', data);
    });

    // Listen for card played events
    gameService.on('card_played', (data) => {
      console.log('SpectatorView - Received card_played event:', data);
      const { playerId, card } = data;

      // Add the played card to our local state
      setPlayedCards(prev => [...prev, { ...card, playedBy: playerId }]);
    });

    // Listen for hand complete events
    gameService.on('hand_complete', (data) => {
      console.log('SpectatorView - Received hand_complete event:', data);
      // Clear played cards when the hand is complete
      setPlayedCards([]);
    });

    // Listen for player turn events
    gameService.on('player_turn', (data) => {
      console.log('SpectatorView - Received player_turn event:', data);
      setCurrentTurnPlayerId(data.playerId);
    });

    return () => {
      gameService.off('spectator_game_state');
      gameService.off('card_played');
      gameService.off('hand_complete');
      gameService.off('player_turn');
    };
  }, [isSpectator, gameCode, spectatorName, gameLobby, playerCards, players]);

  // If not in spectator mode, show a message instead of returning null
  if (!isSpectator || !gameLobby) {
    console.log('SpectatorView - Not rendering, isSpectator:', isSpectator, 'gameLobby:', gameLobby);
    return (
      <div className="fixed inset-0 bg-black/90 z-50 flex flex-col items-center justify-center">
        <div className="bg-gray-900 p-6 rounded-lg border border-[#E1C760] max-w-md w-full">
          <h2 className="text-[#E1C760] text-xl font-bold mb-4">Spectator Mode Error</h2>
          <p className="text-white mb-4">
            {!isSpectator
              ? "You are not in spectator mode. Please join a game as a spectator."
              : "Game information is not available. Please try again."}
          </p>
          <Button
            variant="outline"
            className="w-full border-[#E1C760] text-[#E1C760]"
            onClick={() => navigate('/')}
          >
            Back to Home
          </Button>
        </div>
      </div>
    );
  }

  // Handle leaving spectator mode
  const handleLeaveSpectatorMode = async () => {
    try {
      await leaveSpectatorMode();
      navigate('/');
    } catch (error) {
      console.error('Error leaving spectator mode:', error);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/90 z-50 flex flex-col">
      {/* Header */}
      <div className="bg-gray-900 p-3 flex justify-between items-center border-b border-[#E1C760]/50">
        <div className="flex items-center">
          <div className="bg-[#E1C760] text-black px-3 py-1 rounded-full text-sm font-medium">
            Spectator: {spectatorName}
          </div>
          <div className="ml-3 text-[#E1C760]">
            {gameLobby.teamNames[1]} vs {gameLobby.teamNames[2]}
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            className="border-[#E1C760] text-[#E1C760]"
            onClick={() => setShowAllCards(!showAllCards)}
          >
            {showAllCards ? <EyeOff className="h-4 w-4 mr-1" /> : <Eye className="h-4 w-4 mr-1" />}
            {showAllCards ? "Hide Cards" : "Show Cards"}
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="border-red-500 text-red-500 hover:bg-red-500/10"
            onClick={handleLeaveSpectatorMode}
          >
            <X className="h-4 w-4 mr-1" />
            Exit
          </Button>
        </div>
      </div>

      {/* Game info bar */}
      <div className="bg-gray-800 p-2 flex justify-between items-center">
        <div>
          {trumpSuit && (
            <div className="inline-block">
              <span className="text-[#E1C760]">Trump Suit: </span>
              <span className="font-bold text-white capitalize">{trumpSuit}</span>
            </div>
          )}
        </div>
        <div>
          {currentTurnPlayerId && players.find(p => p.id === currentTurnPlayerId) && (
            <div className="text-white">
              <span className="text-[#E1C760]">Current Turn: </span>
              <span className="font-bold">{players.find(p => p.id === currentTurnPlayerId)?.name}</span>
            </div>
          )}
        </div>
      </div>

      {/* Played cards area */}
      <div className="bg-gray-900 p-4 flex justify-center items-center">
        <div className="flex gap-4">
          {playedCards.length > 0 ? (
            playedCards.map((card, index) => {
              const player = players.find(p => p.id === card.playedBy);
              const teamColor = player?.team === 1 ? 'border-blue-600' : 'border-red-600';

              return (
                <div key={`${card.id}-${index}`} className="flex flex-col items-center">
                  <div className={`w-20 h-28 relative border-2 ${teamColor} rounded-md overflow-hidden`}>
                    <Card className="w-full h-full flex items-center justify-center bg-white">
                      <img
                        src={getCardImagePath(card.value, card.suit)}
                        alt={`${card.value} of ${card.suit}`}
                        className="w-full h-full object-contain"
                      />
                    </Card>
                  </div>
                  <div className="mt-1 text-xs text-white text-center">
                    {player?.name || 'Unknown'}
                  </div>
                </div>
              );
            })
          ) : (
            <div className="text-gray-500">No cards played yet</div>
          )}
        </div>
      </div>

      {/* Player cards */}
      {showAllCards && (
        <div className="flex-1 grid grid-cols-2 grid-rows-2 gap-2 p-2">
          {players && players.length > 0 ? (
            players.map((player) => {
              const cards = playerCards[player.id] || [];
              const teamColor = player.team === 1 ? 'border-blue-600' : 'border-red-600';
              const isDealer = player.isDealer ? 'bg-green-900/30' : '';
              const isTrumpSelector = player.isTrumpSelector ? 'bg-purple-900/30' : '';

              return (
                <div
                  key={player.id}
                  className={`border ${teamColor} rounded-lg p-2 flex flex-col ${isDealer} ${isTrumpSelector}`}
                >
                  <div className="flex items-center mb-2">
                    <div className="w-8 h-8 rounded-full overflow-hidden border-2 border-gray-600">
                      <img
                        src={player.avatar || 'https://via.placeholder.com/40'}
                        alt={player.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="ml-2">
                      <div className="font-medium text-white">{player.name}</div>
                      <div className="text-xs text-gray-400">
                        Team {player.team} {player.isDealer && '• Dealer'} {player.isTrumpSelector && '• Trumper'}
                      </div>
                    </div>
                  </div>

                  <div className="flex-1 flex flex-wrap gap-1 justify-center">
                    {cards.length === 0 ? (
                      <div className="text-gray-500 text-sm flex items-center justify-center h-full">
                        No cards
                      </div>
                    ) : (
                      cards.map((card) => (
                        <div key={card.id} className="w-16 h-24 relative">
                          <Card className="w-full h-full flex items-center justify-center bg-white">
                            <img
                              src={getCardImagePath(card.value, card.suit)}
                              alt={`${card.value} of ${card.suit}`}
                              className="w-full h-full object-contain"
                            />
                          </Card>
                        </div>
                      ))
                    )}
                  </div>
                </div>
              );
            })
          ) : (
            <div className="col-span-2 row-span-2 flex items-center justify-center">
              <div className="bg-gray-800 p-6 rounded-lg text-center">
                <div className="text-[#E1C760] text-lg font-semibold mb-2">Waiting for player data...</div>
                <p className="text-gray-400">Player information will appear here once the game starts.</p>
                <div className="w-12 h-12 border-4 border-[#E1C760] border-t-transparent rounded-full animate-spin mx-auto mt-4"></div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
