import { motion, AnimatePresence } from "framer-motion";
import { useEffect, useState } from "react";
import socketService from "@/services/socketService";
import gameService from '../services/gameService';

export default function CardPassingStatus() {
  // Removed unused destructured elements from useGameStore
  const [gamePhase, setGamePhase] = useState<string>("idle");
  const [cardsDealt, setCardsDealt] = useState(0);
  const [totalCardsToDeal, setTotalCardsToDeal] = useState(24); // 6 cards × 4 players

  // Simulate phases based on cards dealt
  useEffect(() => {
    if (cardsDealt === 0) {
      setGamePhase("idle");
    } else if (cardsDealt < 16) {
      // 4 cards × 4 players
      setGamePhase("passingInitialFour");
    } else if (cardsDealt < 24) {
      // 6 cards × 4 players
      setGamePhase("passingFinalTwo");
    } else {
      setGamePhase("dealingComplete");
    }
  }, [cardsDealt]);

  // State to track the current player receiving a card
  const [currentPlayerInfo, setCurrentPlayerInfo] = useState<{
    name: string;
    team: number;
    card?: { suit: string; value: string };
  } | null>(null);

  // Listen for dealing_card_to events from the server
  useEffect(() => {
    const handleDealingCardTo = (data: {
      playerId: string;
      playerName: string;
      playerTeam: number;
    }) => {
      // Update the current player info
      setCurrentPlayerInfo({
        name: data.playerName,
        team: data.playerTeam
      });
    };

    // Add socket event listener
    gameService.on("dealing_card_to", handleDealingCardTo);

    // Clean up
    return () => {
      gameService.off("dealing_card_to", handleDealingCardTo);
    };
  }, []);

  // Listen for card_dealt events from the server
  useEffect(() => {
    const handleCardDealt = (data: {
      playerId: string;
      playerName: string;
      playerTeam: number;
      card: { suit: string; value: string };
      cardsDealtToPlayer: number;
      totalCardsDealt: number;
    }) => {
      // Increment the cards dealt counter
      setCardsDealt(data.totalCardsDealt);

      // Update the current player info with the card
      setCurrentPlayerInfo({
        name: data.playerName,
        team: data.playerTeam,
        card: data.card
      });

      console.log("Card dealt event received in CardPassingStatus:", data);
    };

    const handleDealingComplete = () => {
      console.log("Dealing complete event received in CardPassingStatus");
      setGamePhase("dealingComplete");

      // Hide the status after a delay
      setTimeout(() => {
        setGamePhase("idle");
      }, 2000);
    };

    // Add socket event listener
    gameService.on("card_dealt", handleCardDealt);
    window.addEventListener("cards_dealing_complete", handleDealingComplete);

    // Clean up
    return () => {
      gameService.off("card_dealt", handleCardDealt);
      window.removeEventListener(
        "cards_dealing_complete",
        handleDealingComplete
      );
    };
  }, []);

  // Determine if the status should be shown
  const isCardPassingPhase =
    gamePhase === "passingInitialFour" || gamePhase === "passingFinalTwo";

  // Debug logging
  useEffect(() => {
    console.log("CardPassingStatus - Current game phase:", gamePhase);
    console.log(
      "CardPassingStatus - Is card passing phase:",
      isCardPassingPhase
    );
    console.log("CardPassingStatus - Cards dealt:", cardsDealt);
  }, [gamePhase, isCardPassingPhase, cardsDealt]);

  // Get the appropriate message based on the game phase and current player
  const getMessage = () => {
    // Base message with card count
    let baseMessage = "";
    if (gamePhase === "passingInitialFour") {
      baseMessage = `Dealing first 4 cards to each player... (${cardsDealt}/16)`;
    } else if (gamePhase === "passingFinalTwo") {
      baseMessage = `Dealing 2 more cards to each player... (${cardsDealt}/24)`;
    } else {
      baseMessage = `Dealing cards... (${cardsDealt}/${totalCardsToDeal})`;
    }

    // Add current player info if available
    if (currentPlayerInfo) {
      if (currentPlayerInfo.card) {
        // If we have card info, show what card was dealt to which player
        return `${baseMessage} - ${currentPlayerInfo.name} (Team ${currentPlayerInfo.team}) received ${currentPlayerInfo.card.value} of ${currentPlayerInfo.card.suit}`;
      } else {
        // If we don't have card info yet, show which player is about to receive a card
        return `${baseMessage} - Dealing to ${currentPlayerInfo.name} (Team ${currentPlayerInfo.team})`;
      }
    }

    return baseMessage;
  };

  // Only show when cards are being passed
  const shouldShowStatus =
    isCardPassingPhase || gamePhase === "dealingComplete";

  if (!shouldShowStatus) {
    return null;
  }

  return (
    <AnimatePresence>
      {shouldShowStatus && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          className="fixed top-20 left-1/2 transform -translate-x-1/2 z-40 bg-black/80 text-[#E1C760] px-4 py-2 rounded-lg shadow-lg border border-[#E1C760]"
        >
          <div className="flex items-center gap-2">
            {gamePhase !== "dealingComplete" ? (
              <>
                <div className="animate-spin h-4 w-4">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                    />
                  </svg>
                </div>
                <span className="font-medium">{getMessage()}</span>
              </>
            ) : (
              <span className="font-medium">All cards dealt!</span>
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
