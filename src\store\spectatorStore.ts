import { create } from 'zustand';
import socketService from '@/services/socketService';
import gameService from "../services/gameService";

// Define the spectator state
export interface SpectatorState {
  isSpectator: boolean;
  gameCode: string | null;
  spectatorName: string | null;
  gameLobby: {
    lobbyCode: string;
    players: any[];
    teams: {
      1: any[];
      2: any[];
    };
    teamNames: {
      1: string;
      2: string;
    };
    spectatorCount: number;
  } | null;
  playerCards: Record<string, any[]>;
  error: string | null;
}

// Define the spectator actions
export interface SpectatorActions {
  setSpectatorInfo: (info: Partial<SpectatorState>) => void;
  leaveSpectatorMode: () => Promise<void>;
  clearError: () => void;
  updatePlayerCards: (playerId: string, cards: any[]) => void;
}

// Initial state
const initialState: SpectatorState = {
  isSpectator: false,
  gameCode: null,
  spectatorName: null,
  gameLobby: null,
  playerCards: {},
  error: null
};

// Create the store
export const useSpectatorStore = create<SpectatorState & SpectatorActions>((set, get) => ({
  ...initialState,

  // Set spectator info
  setSpectatorInfo: (info) => {
    console.log('Setting spectator info:', info);
    set((state) => ({ ...state, ...info }));
  },

  // Leave spectator mode
  leaveSpectatorMode: async () => {
    try {
      await socketService.sendCustomEvent('leave_spectator_mode', {});
      set(initialState);
      return Promise.resolve();
    } catch (error) {
      set({ error: error instanceof Error ? error.message : 'Failed to leave spectator mode' });
      return Promise.reject(error);
    }
  },

  // Clear error
  clearError: () => {
    set({ error: null });
  },

  // Update player cards
  updatePlayerCards: (playerId, cards) => {
    set((state) => ({
      playerCards: {
        ...state.playerCards,
        [playerId]: cards
      }
    }));
  }
}));

// Set up event listeners for spectator mode
export function setupSpectatorListeners() {
  // Listen for spectator game state updates
  gameService.on('spectator_game_state', (data) => {
    console.log('Received spectator_game_state:', data);
    const { playerCards, ...gameState } = data;
    const currentState = useSpectatorStore.getState();

    // Make sure we're in spectator mode before updating
    if (currentState.isSpectator) {
      useSpectatorStore.getState().setSpectatorInfo({
        playerCards: playerCards || {},
        // Update other game state properties
        gameLobby: {
          ...currentState.gameLobby,
          ...gameState
        }
      });
    } else {
      console.warn('Received spectator_game_state but not in spectator mode');
    }
  });

  // Listen for card dealt events to update player cards
  gameService.on('card_dealt', (data) => {
    console.log('Received card_dealt event:', data);
    const { playerId, card } = data;
    const state = useSpectatorStore.getState();

    // Make sure we're in spectator mode before updating
    if (state.isSpectator) {
      const currentCards = state.playerCards[playerId] || [];
      useSpectatorStore.getState().updatePlayerCards(playerId, [...currentCards, card]);
    } else {
      console.warn('Received card_dealt but not in spectator mode');
    }
  });

  // Listen for card played events to update player cards
  gameService.on('card_played', (data) => {
    console.log('Received card_played event:', data);
    const { playerId, card } = data;
    const state = useSpectatorStore.getState();

    // Make sure we're in spectator mode before updating
    if (state.isSpectator) {
      const currentCards = state.playerCards[playerId] || [];
      const updatedCards = currentCards.filter(c => c.id !== card.id);
      useSpectatorStore.getState().updatePlayerCards(playerId, updatedCards);
    } else {
      console.warn('Received card_played but not in spectator mode');
    }
  });

  // Listen for player updates
  gameService.on('players_updated', (data) => {
    console.log('Received players_updated event:', data);
    const { players, teams } = data;
    const state = useSpectatorStore.getState();

    if (state.isSpectator && state.gameLobby) {
      useSpectatorStore.getState().setSpectatorInfo({
        gameLobby: {
          ...state.gameLobby,
          players,
          teams
        }
      });
    } else if (state.isSpectator) {
      console.warn('Received players_updated but gameLobby is null');
    }
  });

  // Listen for game phase updates
  gameService.on('game_phase_updated', (data) => {
    console.log('Received game_phase_updated event:', data);
    const state = useSpectatorStore.getState();

    if (state.isSpectator && state.gameLobby) {
      useSpectatorStore.getState().setSpectatorInfo({
        gameLobby: {
          ...state.gameLobby,
          gamePhase: data.phase
        }
      });
    } else if (state.isSpectator) {
      console.warn('Received game_phase_updated but gameLobby is null');
    }
  });

  // Listen for spectator-specific errors
  gameService.on('spectator_error', (data) => {
    console.error('Received spectator_error:', data);
    const state = useSpectatorStore.getState();

    if (state.isSpectator) {
      useSpectatorStore.getState().setSpectatorInfo({
        error: data.message || 'An error occurred in spectator mode'
      });
    }
  });

  // Listen for trump suit updates
  gameService.on('trump_selected', (data) => {
    console.log('Received trump_selected event:', data);
    const state = useSpectatorStore.getState();

    if (state.isSpectator && state.gameLobby) {
      useSpectatorStore.getState().setSpectatorInfo({
        gameLobby: {
          ...state.gameLobby,
          trumpSuit: data.suit
        }
      });
    }
  });

  // Listen for disconnection events
  gameService.on('disconnect', () => {
    console.log('Socket disconnected in spectator mode');
    // Don't reset the state here, just log it
  });

  // Listen for reconnection events
  gameService.on('connect', () => {
    console.log('Socket reconnected in spectator mode');
    const state = useSpectatorStore.getState();

    // If we were in spectator mode before disconnecting, try to rejoin
    if (state.isSpectator && state.gameCode) {
      console.log('Attempting to rejoin game as spectator after reconnection');
      socketService.sendCustomEvent('join_as_spectator', {
        gameCode: state.gameCode,
        spectatorName: state.spectatorName
      }).then(response => {
        if (response.success) {
          console.log('Successfully rejoined game as spectator');
        } else {
          console.error('Failed to rejoin game as spectator:', response.error);
        }
      }).catch(error => {
        console.error('Error rejoining game as spectator:', error);
      });
    }
  });
}
